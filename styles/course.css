/* Course-specific styles */

.course-hero {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    padding: 120px 0 60px;
    text-align: center;
}

.course-hero-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.course-hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.course-meta {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.course-meta span {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.progress-bar {
    max-width: 400px;
    margin: 0 auto 1rem;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #10b981;
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.9rem;
    opacity: 0.8;
}

.course-content {
    padding: 60px 0;
}

.course-overview {
    text-align: center;
    margin-bottom: 4rem;
}

.course-overview h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: #1e293b;
}

.course-overview p {
    font-size: 1.1rem;
    color: #64748b;
    max-width: 800px;
    margin: 0 auto 2rem;
    line-height: 1.7;
}

.key-takeaways {
    background: #f8fafc;
    padding: 2rem;
    border-radius: 12px;
    max-width: 600px;
    margin: 0 auto;
    text-align: left;
}

.key-takeaways h3 {
    color: #1e293b;
    margin-bottom: 1rem;
    text-align: center;
}

.key-takeaways ul {
    list-style: none;
    padding: 0;
}

.key-takeaways li {
    margin-bottom: 0.75rem;
    color: #475569;
    font-weight: 500;
}

.week-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.week-card {
    background: #fff;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid #e2e8f0;
    transition: transform 0.3s, box-shadow 0.3s;
}

.week-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 40px rgba(0,0,0,0.15);
}

.week-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.week-header h3 {
    color: #1e293b;
    font-size: 1.3rem;
    font-weight: 600;
}

.week-duration {
    background: #e0e7ff;
    color: #3730a3;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.week-description {
    color: #64748b;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.lesson-list {
    list-style: none;
    margin-bottom: 2rem;
}

.lesson-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.lesson-list li:last-child {
    border-bottom: none;
}

.lesson-link {
    color: #2563eb;
    text-decoration: none;
    font-weight: 500;
    display: block;
    transition: color 0.3s;
}

.lesson-link:hover {
    color: #1d4ed8;
}

.lesson-link.completed {
    color: #10b981;
}

.lesson-link.completed::after {
    content: " ✓";
    color: #10b981;
}

.week-link {
    display: inline-block;
    background: #2563eb;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: background 0.3s;
}

.week-link:hover {
    background: #1d4ed8;
}

.course-resources {
    background: #f8fafc;
    padding: 3rem;
    border-radius: 16px;
    margin-top: 4rem;
}

.course-resources h2 {
    text-align: center;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 2rem;
    color: #1e293b;
}

.resource-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.resource-card {
    background: #fff;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    text-align: center;
}

.resource-card h4 {
    color: #1e293b;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.resource-card p {
    color: #64748b;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

.resource-link {
    display: inline-block;
    background: #10b981;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: background 0.3s;
}

.resource-link:hover {
    background: #059669;
}

/* Lesson page styles */
.lesson-hero {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    padding: 120px 0 40px;
}

.lesson-nav {
    background: #fff;
    padding: 1rem 0;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 2rem;
}

.lesson-nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.lesson-breadcrumb {
    color: #64748b;
    font-size: 0.9rem;
}

.lesson-breadcrumb a {
    color: #2563eb;
    text-decoration: none;
}

.lesson-actions {
    display: flex;
    gap: 1rem;
}

.lesson-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 2rem;
}

.lesson-content h1 {
    color: #1e293b;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
}

.lesson-content h2 {
    color: #1e293b;
    font-size: 1.8rem;
    font-weight: 600;
    margin: 2rem 0 1rem;
}

.lesson-content h3 {
    color: #374151;
    font-size: 1.3rem;
    font-weight: 600;
    margin: 1.5rem 0 0.75rem;
}

.lesson-content p {
    color: #4b5563;
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.lesson-content ul, .lesson-content ol {
    color: #4b5563;
    line-height: 1.7;
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.lesson-content li {
    margin-bottom: 0.5rem;
}

.highlight-box {
    background: #fef3c7;
    border-left: 4px solid #f59e0b;
    padding: 1.5rem;
    margin: 2rem 0;
    border-radius: 0 8px 8px 0;
}

.highlight-box h4 {
    color: #92400e;
    margin-bottom: 0.75rem;
    font-weight: 600;
}

.highlight-box p {
    color: #78350f;
    margin-bottom: 0;
}

.info-box {
    background: #dbeafe;
    border-left: 4px solid #3b82f6;
    padding: 1.5rem;
    margin: 2rem 0;
    border-radius: 0 8px 8px 0;
}

.info-box h4 {
    color: #1e40af;
    margin-bottom: 0.75rem;
    font-weight: 600;
}

.info-box p {
    color: #1e3a8a;
    margin-bottom: 0;
}

.lesson-navigation {
    display: flex;
    justify-content: space-between;
    margin-top: 4rem;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
}

.nav-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: #2563eb;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    transition: background 0.3s;
}

.nav-button:hover {
    background: #1d4ed8;
}

.nav-button.disabled {
    background: #9ca3af;
    cursor: not-allowed;
}

.complete-lesson-btn {
    background: #10b981;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s;
}

.complete-lesson-btn:hover {
    background: #059669;
}

.complete-lesson-btn.completed {
    background: #6b7280;
    cursor: default;
}

/* Firm comparison styles */
.firm-comparison-section {
    margin: 4rem 0;
    background: #f8fafc;
    padding: 3rem;
    border-radius: 16px;
}

.firm-comparison-section h2 {
    text-align: center;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 2rem;
    color: #1e293b;
}

.comparison-table {
    overflow-x: auto;
}

.comparison-table table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.comparison-table th {
    background: #2563eb;
    color: white;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
}

.comparison-table td {
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
    color: #374151;
}

.comparison-table tr:last-child td {
    border-bottom: none;
}

.comparison-table tr:nth-child(even) {
    background: #f8fafc;
}

/* Process steps styles */
.prop-firm-process {
    margin: 4rem 0;
    background: #f8fafc;
    padding: 3rem;
    border-radius: 16px;
}

.prop-firm-process h2 {
    text-align: center;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: #1e293b;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.process-step {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    background: #fff;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.step-number {
    background: #2563eb;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.step-content h4 {
    color: #1e293b;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.step-content p {
    color: #64748b;
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
}

/* CAT methodology styles */
.cat-methodology {
    margin: 4rem 0;
    text-align: center;
}

.cat-methodology h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: #1e293b;
}

.methodology-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.methodology-card {
    background: #fff;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid #e2e8f0;
    transition: transform 0.3s;
}

.methodology-card:hover {
    transform: translateY(-5px);
}

.methodology-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.methodology-card h3 {
    color: #1e293b;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.methodology-card p {
    color: #64748b;
    line-height: 1.6;
}

/* Trading rules styles */
.trading-rules-section {
    margin: 4rem 0;
    background: #f8fafc;
    padding: 3rem;
    border-radius: 16px;
}

.trading-rules-section h2 {
    text-align: center;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: #1e293b;
}

.rules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.rule-card {
    background: #fff;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.rule-card.consolidation {
    border-left: 4px solid #3b82f6;
}

.rule-card.direction {
    border-left: 4px solid #10b981;
}

.rule-card.chaos {
    border-left: 4px solid #f59e0b;
}

.rule-card h3 {
    color: #1e293b;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.rule-card ul {
    list-style: none;
    padding: 0;
}

.rule-card li {
    padding: 0.5rem 0;
    color: #374151;
    font-weight: 500;
}

/* Responsive design */
@media (max-width: 768px) {
    .course-hero-content h1 {
        font-size: 2rem;
    }

    .course-meta {
        flex-direction: column;
        gap: 1rem;
    }

    .week-grid {
        grid-template-columns: 1fr;
    }

    .lesson-nav-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .lesson-navigation {
        flex-direction: column;
        gap: 1rem;
    }

    .comparison-table {
        font-size: 0.9rem;
    }

    .comparison-table th,
    .comparison-table td {
        padding: 0.75rem 0.5rem;
    }
}
