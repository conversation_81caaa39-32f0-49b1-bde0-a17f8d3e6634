#!/usr/bin/env python3
"""
Convert HTML to PDF using WeasyPrint
"""

import os
import sys
from weasyprint import HTML, CSS
from weasyprint.text.fonts import FontConfiguration

def convert_html_to_pdf(html_file, pdf_file):
    """Convert HTML file to PDF"""
    
    # Check if HTML file exists
    if not os.path.exists(html_file):
        print(f"Error: HTML file '{html_file}' not found!")
        return False
    
    try:
        print(f"Converting {html_file} to {pdf_file}...")
        
        # Create font configuration
        font_config = FontConfiguration()
        
        # Additional CSS for better PDF output
        pdf_css = CSS(string='''
            @page {
                margin: 1in;
                size: A4;
                @top-center {
                    content: "LearningTradingTrueLevel - Complete Trading Education Guide";
                    font-size: 10pt;
                    color: #666;
                }
                @bottom-center {
                    content: "Page " counter(page);
                    font-size: 10pt;
                    color: #666;
                }
            }
            
            /* Ensure good page breaks */
            .page-break {
                page-break-before: always;
            }
            
            .no-break {
                page-break-inside: avoid;
            }
            
            /* Better table formatting for PDF */
            table {
                page-break-inside: avoid;
                border-collapse: collapse;
                width: 100%;
                margin: 12pt 0;
            }
            
            th, td {
                border: 1pt solid #333;
                padding: 6pt;
                text-align: left;
            }
            
            th {
                background: #f3f4f6;
                font-weight: bold;
            }
            
            /* Better box formatting */
            .key-insight, .warning, .exercise {
                page-break-inside: avoid;
                margin: 12pt 0;
                padding: 12pt;
                border-radius: 6pt;
            }
            
            .key-insight {
                background: #eff6ff;
                border: 2pt solid #2563eb;
            }
            
            .warning {
                background: #fef2f2;
                border: 2pt solid #dc2626;
            }
            
            .exercise {
                background: #f0fdf4;
                border: 2pt solid #059669;
            }
            
            /* Typography improvements */
            body {
                font-family: "Times New Roman", serif;
                font-size: 11pt;
                line-height: 1.6;
                color: #333;
            }
            
            h1 {
                font-size: 20pt;
                color: #2563eb;
                margin: 18pt 0 12pt 0;
            }
            
            h2 {
                font-size: 16pt;
                color: #2563eb;
                margin: 16pt 0 8pt 0;
            }
            
            h3 {
                font-size: 14pt;
                color: #059669;
                margin: 14pt 0 7pt 0;
            }
            
            h4 {
                font-size: 12pt;
                color: #d97706;
                margin: 12pt 0 6pt 0;
            }
            
            p {
                margin: 6pt 0;
                text-align: justify;
            }
            
            ul, ol {
                margin: 6pt 0 6pt 18pt;
            }
            
            li {
                margin: 3pt 0;
            }
        ''', font_config=font_config)
        
        # Convert HTML to PDF
        html_doc = HTML(filename=html_file)
        html_doc.write_pdf(pdf_file, stylesheets=[pdf_css], font_config=font_config)
        
        print(f"✅ Successfully converted to {pdf_file}")
        print(f"📄 PDF file size: {os.path.getsize(pdf_file) / 1024 / 1024:.2f} MB")
        return True
        
    except Exception as e:
        print(f"❌ Error converting to PDF: {str(e)}")
        return False

def main():
    # File paths
    html_file = "comprehensive_trading_guide.html"
    pdf_file = "LearningTradingTrueLevel_Complete_Trading_Guide.pdf"
    
    # Convert HTML to PDF
    success = convert_html_to_pdf(html_file, pdf_file)
    
    if success:
        print("\n🎉 PDF conversion completed successfully!")
        print(f"📁 Output file: {pdf_file}")
        print("\n📋 You can now:")
        print("   • Print the PDF document")
        print("   • Share it digitally")
        print("   • Use it as a reference guide")
    else:
        print("\n❌ PDF conversion failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
