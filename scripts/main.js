// Main JavaScript for ImanTrading Academy

document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('a[href^="#"]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = targetSection.offsetTop - headerHeight;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Add scroll effect to header
    const header = document.querySelector('.header');
    let lastScrollTop = 0;

    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (scrollTop > lastScrollTop && scrollTop > 100) {
            // Scrolling down
            header.style.transform = 'translateY(-100%)';
        } else {
            // Scrolling up
            header.style.transform = 'translateY(0)';
        }
        
        lastScrollTop = scrollTop;
    });

    // Animate course cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe course cards
    const courseCards = document.querySelectorAll('.course-card');
    courseCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });

    // Progress tracking for courses
    function updateProgress(courseId, lessonId) {
        const progress = JSON.parse(localStorage.getItem('courseProgress') || '{}');
        
        if (!progress[courseId]) {
            progress[courseId] = [];
        }
        
        if (!progress[courseId].includes(lessonId)) {
            progress[courseId].push(lessonId);
        }
        
        localStorage.setItem('courseProgress', JSON.stringify(progress));
        updateProgressDisplay(courseId);
    }

    function updateProgressDisplay(courseId) {
        const progress = JSON.parse(localStorage.getItem('courseProgress') || '{}');
        const courseProgress = progress[courseId] || [];
        const progressElement = document.querySelector(`#${courseId}-progress`);
        
        if (progressElement) {
            const totalLessons = document.querySelectorAll(`[data-course="${courseId}"]`).length;
            const completedLessons = courseProgress.length;
            const percentage = Math.round((completedLessons / totalLessons) * 100);
            
            progressElement.textContent = `${percentage}% Complete (${completedLessons}/${totalLessons})`;
        }
    }

    // Initialize progress display
    const courses = ['full-trading-guide', 'best-prop-firms', 'prop-firm-guide', 'categorical-trading'];
    courses.forEach(courseId => {
        updateProgressDisplay(courseId);
    });

    // Trading chart animation
    function animateTradingChart() {
        const chart = document.querySelector('.trading-chart polyline');
        if (chart) {
            const pathLength = chart.getTotalLength();
            chart.style.strokeDasharray = pathLength + ' ' + pathLength;
            chart.style.strokeDashoffset = pathLength;
            
            // Animate the path
            chart.style.transition = 'stroke-dashoffset 3s ease-in-out';
            chart.style.strokeDashoffset = '0';
        }
    }

    // Trigger chart animation when in view
    const chartObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateTradingChart();
                chartObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    const tradingChart = document.querySelector('.trading-chart');
    if (tradingChart) {
        chartObserver.observe(tradingChart);
    }

    // Mobile menu toggle (if needed)
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navMenu = document.querySelector('.nav-menu');

    if (mobileMenuToggle && navMenu) {
        mobileMenuToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
        });
    }

    // Course completion tracking
    function markLessonComplete(courseId, lessonId) {
        updateProgress(courseId, lessonId);
        
        // Visual feedback
        const lessonElement = document.querySelector(`[data-lesson="${lessonId}"]`);
        if (lessonElement) {
            lessonElement.classList.add('completed');
        }
        
        // Show completion message
        showNotification('Lesson completed! 🎉');
    }

    function showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 1rem 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // Export functions for use in other scripts
    window.ImanTradingAcademy = {
        markLessonComplete,
        updateProgress,
        showNotification
    };
});

// Utility functions for course navigation
function navigateToLesson(courseId, weekNumber, lessonNumber) {
    const url = `courses/${courseId}/week-${weekNumber}/lesson-${lessonNumber}.html`;
    window.location.href = url;
}

function navigateToWeek(courseId, weekNumber) {
    const url = `courses/${courseId}/week-${weekNumber}/`;
    window.location.href = url;
}

// Course data structure
const courseStructure = {
    'full-trading-guide': {
        title: 'Full Trading Guide',
        weeks: 3,
        totalLessons: 15
    },
    'best-prop-firms': {
        title: 'Best Futures Prop Firms',
        weeks: 3,
        totalLessons: 12
    },
    'prop-firm-guide': {
        title: 'Prop Firm Trading Guide',
        weeks: 3,
        totalLessons: 12
    },
    'categorical-trading': {
        title: "Iman's Trading Strategy",
        weeks: 3,
        totalLessons: 15
    }
};
